/**
 * <PERSON><PERSON><PERSON>'s algorithm implementation for finding shortest path
 * @param {Object} graph - Graph representation where keys are node IDs and values are objects with neighbor distances
 * @param {string|number} start - Starting node ID
 * @param {string|number} end - Ending node ID
 * @returns {Array} - Array of node IDs representing the shortest path
 */
export function dijkstra(graph, start, end) {
  // Convert to strings for consistent key handling
  start = String(start);
  end = String(end);
  
  // Check if start and end nodes exist in the graph
  if (!graph[start] || !graph[end]) {
    console.warn('Start or end node not found in graph');
    return [];
  }

  // Initialize distances and previous nodes
  const distances = {};
  const previous = {};
  const unvisited = new Set();

  // Initialize all distances to infinity except start
  for (const node in graph) {
    distances[node] = node === start ? 0 : Infinity;
    previous[node] = null;
    unvisited.add(node);
  }

  while (unvisited.size > 0) {
    // Find unvisited node with minimum distance
    let currentNode = null;
    let minDistance = Infinity;
    
    for (const node of unvisited) {
      if (distances[node] < minDistance) {
        minDistance = distances[node];
        currentNode = node;
      }
    }

    // If no reachable unvisited node found, break
    if (currentNode === null || distances[currentNode] === Infinity) {
      break;
    }

    // Remove current node from unvisited set
    unvisited.delete(currentNode);

    // If we reached the destination, we can stop
    if (currentNode === end) {
      break;
    }

    // Update distances to neighbors
    const neighbors = graph[currentNode] || {};
    for (const neighbor in neighbors) {
      if (unvisited.has(neighbor)) {
        const edgeWeight = neighbors[neighbor];
        const newDistance = distances[currentNode] + edgeWeight;
        
        if (newDistance < distances[neighbor]) {
          distances[neighbor] = newDistance;
          previous[neighbor] = currentNode;
        }
      }
    }
  }

  // Reconstruct path
  const path = [];
  let currentNode = end;
  
  while (currentNode !== null) {
    path.unshift(parseInt(currentNode)); // Convert back to number
    currentNode = previous[currentNode];
  }

  // If path doesn't start with the start node, no path was found
  if (path[0] !== parseInt(start)) {
    return [];
  }

  return path;
}

/**
 * A* algorithm implementation (alternative pathfinding algorithm)
 * @param {Object} graph - Graph representation
 * @param {string|number} start - Starting node ID
 * @param {string|number} end - Ending node ID
 * @param {Function} heuristic - Heuristic function for A*
 * @returns {Array} - Array of node IDs representing the shortest path
 */
export function aStar(graph, start, end, heuristic = () => 0) {
  start = String(start);
  end = String(end);
  
  if (!graph[start] || !graph[end]) {
    return [];
  }

  const openSet = new Set([start]);
  const closedSet = new Set();
  const gScore = { [start]: 0 };
  const fScore = { [start]: heuristic(start, end) };
  const previous = {};

  while (openSet.size > 0) {
    // Find node in openSet with lowest fScore
    let current = null;
    let lowestF = Infinity;
    
    for (const node of openSet) {
      const f = fScore[node] || Infinity;
      if (f < lowestF) {
        lowestF = f;
        current = node;
      }
    }

    if (current === end) {
      // Reconstruct path
      const path = [];
      let node = end;
      while (node) {
        path.unshift(parseInt(node));
        node = previous[node];
      }
      return path;
    }

    openSet.delete(current);
    closedSet.add(current);

    const neighbors = graph[current] || {};
    for (const neighbor in neighbors) {
      if (closedSet.has(neighbor)) continue;

      const tentativeG = gScore[current] + neighbors[neighbor];

      if (!openSet.has(neighbor)) {
        openSet.add(neighbor);
      } else if (tentativeG >= (gScore[neighbor] || Infinity)) {
        continue;
      }

      previous[neighbor] = current;
      gScore[neighbor] = tentativeG;
      fScore[neighbor] = tentativeG + heuristic(neighbor, end);
    }
  }

  return []; // No path found
}

/**
 * Calculate Euclidean distance between two points
 * @param {Object} point1 - {x, y} coordinates
 * @param {Object} point2 - {x, y} coordinates
 * @returns {number} - Euclidean distance
 */
export function euclideanDistance(point1, point2) {
  return Math.sqrt(
    Math.pow(point1.x - point2.x, 2) + 
    Math.pow(point1.y - point2.y, 2)
  );
}

/**
 * Calculate Manhattan distance between two points
 * @param {Object} point1 - {x, y} coordinates
 * @param {Object} point2 - {x, y} coordinates
 * @returns {number} - Manhattan distance
 */
export function manhattanDistance(point1, point2) {
  return Math.abs(point1.x - point2.x) + Math.abs(point1.y - point2.y);
}
