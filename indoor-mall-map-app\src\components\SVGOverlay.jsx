import React, { useEffect, useRef, useState } from 'react';
import { useMallData } from '../context/MallDataContext';

const SVGOverlay = ({ 
  map, 
  selectedFloor, 
  onBoothClick, 
  onRouteSelection, 
  routeStart, 
  routeEnd 
}) => {
  const svgRef = useRef(null);
  const [svgDimensions, setSvgDimensions] = useState({ width: 1000, height: 1000 });
  const { mallData } = useMallData();

  useEffect(() => {
    if (!map || !svgRef.current) return;

    const updateSVGPosition = () => {
      const canvas = map.getCanvas();
      const rect = canvas.getBoundingClientRect();
      
      // Position SVG overlay to match the map
      const svg = svgRef.current;
      svg.style.position = 'absolute';
      svg.style.top = '0';
      svg.style.left = '0';
      svg.style.width = `${rect.width}px`;
      svg.style.height = `${rect.height}px`;
      svg.style.pointerEvents = 'auto';
      svg.style.zIndex = '1000';
    };

    // Update SVG position on map events
    map.on('move', updateSVGPosition);
    map.on('zoom', updateSVGPosition);
    map.on('resize', updateSVGPosition);

    // Initial positioning
    updateSVGPosition();

    return () => {
      map.off('move', updateSVGPosition);
      map.off('zoom', updateSVGPosition);
      map.off('resize', updateSVGPosition);
    };
  }, [map]);

  const handleBoothClick = (booth, event) => {
    event.stopPropagation();
    onBoothClick(booth);
    onRouteSelection(booth);
  };

  const getBoothColor = (booth) => {
    if (routeStart && booth.id === routeStart.id) return '#10b981'; // green
    if (routeEnd && booth.id === routeEnd.id) return '#ef4444'; // red
    return booth.available ? '#3b82f6' : '#6b7280'; // blue or gray
  };

  const getBoothStroke = (booth) => {
    if (routeStart && booth.id === routeStart.id) return '#059669';
    if (routeEnd && booth.id === routeEnd.id) return '#dc2626';
    return '#1e40af';
  };

  // Convert world coordinates to SVG coordinates
  const worldToSVG = (worldX, worldY) => {
    const canvas = map.getCanvas();
    const rect = canvas.getBoundingClientRect();
    
    // This is a simplified conversion - in a real app, you'd use proper coordinate transformation
    const svgX = (worldX / 1000) * rect.width;
    const svgY = (worldY / 1000) * rect.height;
    
    return { x: svgX, y: svgY };
  };

  const currentFloorData = mallData.floors.find(floor => floor.id === selectedFloor);
  
  if (!currentFloorData) {
    return null;
  }

  return (
    <svg
      ref={svgRef}
      className="absolute top-0 left-0 pointer-events-auto"
      style={{ zIndex: 1000 }}
    >
      {/* Floor Plan Background */}
      <defs>
        <pattern id="floorPattern" patternUnits="userSpaceOnUse" width="50" height="50">
          <rect width="50" height="50" fill="#f8fafc" stroke="#e2e8f0" strokeWidth="1"/>
        </pattern>
      </defs>
      
      {/* Hallways */}
      {currentFloorData.hallways.map((hallway) => {
        const start = worldToSVG(hallway.start.x, hallway.start.y);
        const end = worldToSVG(hallway.end.x, hallway.end.y);
        
        return (
          <line
            key={hallway.id}
            x1={start.x}
            y1={start.y}
            x2={end.x}
            y2={end.y}
            stroke="#d1d5db"
            strokeWidth="8"
            strokeLinecap="round"
          />
        );
      })}

      {/* Booths */}
      {currentFloorData.booths.map((booth) => {
        const position = worldToSVG(booth.position.x, booth.position.y);
        const width = (booth.size.width / 1000) * svgDimensions.width;
        const height = (booth.size.height / 1000) * svgDimensions.height;
        
        return (
          <g key={booth.id}>
            {/* Booth Rectangle */}
            <rect
              x={position.x - width / 2}
              y={position.y - height / 2}
              width={width}
              height={height}
              fill={getBoothColor(booth)}
              stroke={getBoothStroke(booth)}
              strokeWidth="2"
              rx="4"
              className="cursor-pointer hover:opacity-80 transition-opacity"
              onClick={(e) => handleBoothClick(booth, e)}
            />
            
            {/* Booth Label */}
            <text
              x={position.x}
              y={position.y}
              textAnchor="middle"
              dominantBaseline="middle"
              fill="white"
              fontSize="12"
              fontWeight="bold"
              className="pointer-events-none select-none"
            >
              {booth.name}
            </text>
            
            {/* Booth Number */}
            <text
              x={position.x}
              y={position.y + 15}
              textAnchor="middle"
              dominantBaseline="middle"
              fill="white"
              fontSize="10"
              className="pointer-events-none select-none"
            >
              #{booth.id}
            </text>
          </g>
        );
      })}

      {/* Floor Plan Boundaries */}
      <rect
        x="10"
        y="10"
        width={svgDimensions.width - 20}
        height={svgDimensions.height - 20}
        fill="none"
        stroke="#374151"
        strokeWidth="3"
        strokeDasharray="10,5"
        rx="8"
      />

      {/* Floor Label */}
      <text
        x="30"
        y="40"
        fill="#374151"
        fontSize="16"
        fontWeight="bold"
        className="pointer-events-none select-none"
      >
        Floor {selectedFloor}
      </text>

      {/* Legend */}
      <g transform="translate(30, 60)">
        <rect x="0" y="0" width="150" height="80" fill="white" fillOpacity="0.9" stroke="#d1d5db" strokeWidth="1" rx="4"/>
        
        <text x="10" y="15" fontSize="12" fontWeight="bold" fill="#374151">Legend:</text>
        
        <rect x="10" y="25" width="12" height="8" fill="#3b82f6" rx="2"/>
        <text x="28" y="32" fontSize="10" fill="#374151">Available Booth</text>
        
        <rect x="10" y="40" width="12" height="8" fill="#6b7280" rx="2"/>
        <text x="28" y="47" fontSize="10" fill="#374151">Occupied Booth</text>
        
        <rect x="10" y="55" width="12" height="8" fill="#10b981" rx="2"/>
        <text x="28" y="62" fontSize="10" fill="#374151">Route Start</text>
        
        <rect x="10" y="70" width="12" height="8" fill="#ef4444" rx="2"/>
        <text x="28" y="77" fontSize="10" fill="#374151">Route End</text>
      </g>
    </svg>
  );
};

export default SVGOverlay;
