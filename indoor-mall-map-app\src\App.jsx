import React, { useState } from 'react';
import MapComponent from './components/MapComponent';
import FloorSelector from './components/FloorSelector';
import BoothInfoModal from './components/BoothInfoModal';
import RouteSelector from './components/RouteSelector';
import { MallDataProvider } from './context/MallDataContext';

function App() {
  const [selectedFloor, setSelectedFloor] = useState(1);
  const [selectedBooth, setSelectedBooth] = useState(null);
  const [routeStart, setRouteStart] = useState(null);
  const [routeEnd, setRouteEnd] = useState(null);
  const [showBoothModal, setShowBoothModal] = useState(false);

  const handleBoothClick = (booth) => {
    setSelectedBooth(booth);
    setShowBoothModal(true);
  };

  const handleRouteSelection = (booth) => {
    if (!routeStart) {
      setRouteStart(booth);
    } else if (!routeEnd && booth.id !== routeStart.id) {
      setRouteEnd(booth);
    } else {
      // Reset and start new route
      setRouteStart(booth);
      setRouteEnd(null);
    }
  };

  return (
    <MallDataProvider>
      <div className="h-screen flex flex-col bg-gray-100">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200 p-4">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <h1 className="text-xl sm:text-2xl font-bold text-gray-900">
              Indoor Mall Navigator
            </h1>
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4 w-full sm:w-auto">
              <FloorSelector
                selectedFloor={selectedFloor}
                onFloorChange={setSelectedFloor}
              />
              <RouteSelector
                routeStart={routeStart}
                routeEnd={routeEnd}
                onClearRoute={() => {
                  setRouteStart(null);
                  setRouteEnd(null);
                }}
              />
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1 relative">
          <MapComponent
            selectedFloor={selectedFloor}
            onBoothClick={handleBoothClick}
            onRouteSelection={handleRouteSelection}
            routeStart={routeStart}
            routeEnd={routeEnd}
          />
        </main>

        {/* Booth Info Modal */}
        {showBoothModal && selectedBooth && (
          <BoothInfoModal
            booth={selectedBooth}
            onClose={() => setShowBoothModal(false)}
          />
        )}
      </div>
    </MallDataProvider>
  );
}

export default App;
