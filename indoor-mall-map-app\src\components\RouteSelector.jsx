import React from 'react';

const RouteSelector = ({ routeStart, routeEnd, onClearRoute }) => {
  const hasRoute = routeStart && routeEnd;
  const hasPartialRoute = routeStart && !routeEnd;

  return (
    <div className="flex items-center space-x-3">
      {/* Route Status Display */}
      <div className="flex items-center space-x-2 text-sm">
        {hasRoute && (
          <div className="flex items-center space-x-2 bg-green-50 border border-green-200 rounded-lg px-3 py-2">
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-green-700 font-medium">{routeStart.name}</span>
            </div>
            <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
              <span className="text-red-700 font-medium">{routeEnd.name}</span>
            </div>
          </div>
        )}

        {hasPartialRoute && (
          <div className="flex items-center space-x-2 bg-blue-50 border border-blue-200 rounded-lg px-3 py-2">
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-blue-700 font-medium">{routeStart.name}</span>
            </div>
            <span className="text-blue-600 text-xs">→ Select destination</span>
          </div>
        )}

        {!routeStart && !routeEnd && (
          <div className="text-gray-500 text-xs">
            Click two booths to plan a route
          </div>
        )}
      </div>

      {/* Clear Route Button */}
      {(routeStart || routeEnd) && (
        <button
          onClick={onClearRoute}
          className="flex items-center space-x-1 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
          <span>Clear</span>
        </button>
      )}

      {/* Route Info */}
      {hasRoute && (
        <div className="hidden lg:block text-xs text-gray-500">
          Route calculated using Dijkstra's algorithm
        </div>
      )}
    </div>
  );
};

export default RouteSelector;
