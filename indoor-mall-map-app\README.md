# Indoor Mall Map Application

A modern, interactive indoor mall navigation application built with React, Tailwind CSS, and Mapbox GL JS. This application provides an intuitive way to explore mall layouts, find booths, and calculate optimal walking routes between locations.

## 🌟 Features

### Core Functionality
- **Interactive Mapbox Integration**: Real-world GPS-centered background map with smooth navigation controls
- **SVG Floorplan Overlay**: Interactive mall floorplans overlaid on the map with clickable booth shapes
- **Multi-Floor Support**: Navigate between different floors with an intuitive floor selector
- **Booth Information System**: Click on any booth to view detailed information in a modal
- **Smart Route Planning**: Select two booths to automatically calculate and display the shortest walking route
- **Graph-Based Pathfinding**: Uses <PERSON><PERSON><PERSON>'s algorithm for optimal route calculation
- **Responsive Design**: Fully optimized for both mobile and desktop devices

### Technical Features
- **Real-time Route Visualization**: Dynamic route rendering with start/end markers
- **Layer Management**: Toggle between floors while maintaining map state
- **Coordinate Transformation**: Seamless conversion between world and map coordinates
- **Performance Optimized**: Efficient rendering and state management
- **Accessibility**: Screen reader friendly with proper ARIA labels

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn
- Mapbox account and access token

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd indoor-mall-map-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```

   Edit the `.env` file and add your Mapbox access token:
   ```env
   VITE_MAPBOX_ACCESS_TOKEN=your_mapbox_access_token_here
   VITE_MALL_LATITUDE=40.758
   VITE_MALL_LONGITUDE=-73.985
   VITE_MALL_ZOOM=18
   ```

4. **Start the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to `http://localhost:5173` to view the application.

## 🗺️ Getting a Mapbox Access Token

1. Visit [Mapbox Account](https://account.mapbox.com/access-tokens/)
2. Sign up for a free account or log in
3. Create a new access token with the following scopes:
   - `styles:read`
   - `fonts:read`
   - `datasets:read`
4. Copy the token and add it to your `.env` file

## 📱 Usage

### Basic Navigation
- **Zoom**: Use mouse wheel or pinch gestures on mobile
- **Pan**: Click and drag to move around the map
- **Floor Selection**: Use the floor selector in the header to switch between levels

### Booth Interaction
- **View Information**: Click on any booth to see detailed information
- **Route Planning**: Click on two different booths to calculate a route between them
- **Clear Route**: Use the "Clear" button in the route selector to reset

### Route Planning
1. Click on your starting booth (it will turn green)
2. Click on your destination booth (it will turn red)
3. The shortest route will be automatically calculated and displayed
4. Route information appears in the bottom panel

## 🏗️ Project Structure

```
src/
├── components/           # React components
│   ├── MapComponent.jsx     # Main map container with Mapbox integration
│   ├── SVGOverlay.jsx       # Interactive SVG floorplan overlay
│   ├── FloorSelector.jsx    # Floor selection component
│   ├── BoothInfoModal.jsx   # Booth information modal
│   └── RouteSelector.jsx    # Route planning controls
├── context/             # React context providers
│   └── MallDataContext.jsx  # Mall data and pathfinding logic
├── utils/               # Utility functions
│   └── pathfinding.js       # Dijkstra's algorithm implementation
├── App.jsx              # Main application component
├── main.jsx            # Application entry point
└── index.css           # Global styles with Tailwind directives
```

## 🛠️ Technology Stack

- **Frontend Framework**: React 18 with Vite
- **Styling**: Tailwind CSS for responsive design
- **Mapping**: Mapbox GL JS for interactive maps
- **State Management**: React Context API
- **Pathfinding**: Custom Dijkstra's algorithm implementation
- **Build Tool**: Vite for fast development and building
- **Package Manager**: npm

## 🧮 Pathfinding Algorithm

The application uses **Dijkstra's algorithm** for route calculation:

- **Graph Construction**: Booths are nodes, distances are edge weights
- **Dynamic Routing**: Routes recalculated when floor or selection changes
- **Optimal Paths**: Guarantees shortest walking distance
- **Performance**: Efficient O((V + E) log V) complexity

### Algorithm Features
- Handles disconnected booth clusters
- Supports multi-floor routing (future enhancement)
- Fallback to A* algorithm available
- Distance-based edge weighting

## 📊 Sample Data Structure

The application includes comprehensive sample data:

```javascript
{
  floors: [
    {
      id: 1,
      name: "Ground Floor",
      booths: [
        {
          id: 1,
          name: "Coffee Corner",
          category: "Food & Beverage",
          position: { x: 200, y: 200 },
          size: { width: 80, height: 60 },
          available: true,
          amenities: ["WiFi", "Seating"],
          contact: { phone: "******-0101" }
        }
      ],
      hallways: [
        { id: 1, start: { x: 100, y: 200 }, end: { x: 700, y: 200 } }
      ]
    }
  ]
}
```

## 🎨 Customization

### Adding New Floors
1. Edit `src/context/MallDataContext.jsx`
2. Add new floor objects to the `floors` array
3. Include booth and hallway data for the new floor

### Modifying Booth Data
- Update booth properties in the context file
- Add new categories, amenities, or contact information
- Adjust booth positions and sizes as needed

### Styling Customization
- Modify Tailwind classes in components
- Update colors in `tailwind.config.js`
- Customize SVG styling in `SVGOverlay.jsx`

## 🧪 Testing

### Running Tests
```bash
npm run test
```

### Manual Testing Checklist
- [ ] Map loads with correct center and zoom
- [ ] Floor selector switches between floors
- [ ] Booths are clickable and show information
- [ ] Route calculation works between any two booths
- [ ] Route visualization displays correctly
- [ ] Responsive design works on mobile devices
- [ ] All interactive elements are accessible

## 🚀 Deployment

### Build for Production
```bash
npm run build
```

### Deploy to Vercel
```bash
npm install -g vercel
vercel --prod
```

### Deploy to Netlify
1. Build the project: `npm run build`
2. Upload the `dist` folder to Netlify
3. Set environment variables in Netlify dashboard

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Mapbox](https://www.mapbox.com/) for the mapping platform
- [Tailwind CSS](https://tailwindcss.com/) for the utility-first CSS framework
- [React](https://reactjs.org/) for the component-based UI library
- [Vite](https://vitejs.dev/) for the fast build tool

## 📞 Support

If you have any questions or need help with setup, please:
1. Check the [Issues](https://github.com/your-repo/issues) page
2. Create a new issue with detailed information
3. Include your environment details and error messages

---

**Happy mapping! 🗺️✨**
