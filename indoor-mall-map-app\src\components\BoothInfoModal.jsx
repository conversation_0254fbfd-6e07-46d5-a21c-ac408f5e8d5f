import React from 'react';

const BoothInfoModal = ({ booth, onClose }) => {
  if (!booth) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[80vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {booth.name}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Booth Status */}
          <div className="flex items-center mb-4">
            <div className={`w-3 h-3 rounded-full mr-2 ${
              booth.available ? 'bg-green-500' : 'bg-red-500'
            }`}></div>
            <span className={`text-sm font-medium ${
              booth.available ? 'text-green-700' : 'text-red-700'
            }`}>
              {booth.available ? 'Available' : 'Occupied'}
            </span>
          </div>

          {/* Booth Details */}
          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-1">Booth ID</h3>
              <p className="text-sm text-gray-900">#{booth.id}</p>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-1">Category</h3>
              <p className="text-sm text-gray-900">{booth.category}</p>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-1">Description</h3>
              <p className="text-sm text-gray-900">{booth.description}</p>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-1">Size</h3>
              <p className="text-sm text-gray-900">
                {booth.size.width} × {booth.size.height} units
              </p>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-1">Floor</h3>
              <p className="text-sm text-gray-900">Floor {booth.floor}</p>
            </div>

            {booth.amenities && booth.amenities.length > 0 && (
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Amenities</h3>
                <div className="flex flex-wrap gap-2">
                  {booth.amenities.map((amenity, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                    >
                      {amenity}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {booth.contact && (
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-1">Contact</h3>
                <div className="text-sm text-gray-900 space-y-1">
                  {booth.contact.phone && (
                    <p>📞 {booth.contact.phone}</p>
                  )}
                  {booth.contact.email && (
                    <p>✉️ {booth.contact.email}</p>
                  )}
                </div>
              </div>
            )}

            {booth.hours && (
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-1">Operating Hours</h3>
                <p className="text-sm text-gray-900">{booth.hours}</p>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors"
          >
            Close
          </button>
          <button
            onClick={() => {
              // In a real app, this could open directions or more details
              alert(`Getting directions to ${booth.name}...`);
            }}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
          >
            Get Directions
          </button>
        </div>
      </div>
    </div>
  );
};

export default BoothInfoModal;
