import React, { useRef, useEffect, useState } from 'react';
import mapboxgl from 'mapbox-gl';
import { useMallData } from '../context/MallDataContext';
import SVGOverlay from './SVGOverlay';

// Get Mapbox access token from environment variables
const MAPBOX_ACCESS_TOKEN = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN || 'pk.eyJ1IjoieW91ci11c2VybmFtZSIsImEiOiJjbGV4YW1wbGUifQ.example_token_here';

const MapComponent = ({
  selectedFloor,
  onBoothClick,
  onRouteSelection,
  routeStart,
  routeEnd
}) => {
  const mapContainer = useRef(null);
  const map = useRef(null);
  const [mapLoaded, setMapLoaded] = useState(false);
  const { mallData, calculateRoute } = useMallData();

  // Mall location from environment variables or default to Times Square, NYC
  const defaultCenter = [
    parseFloat(import.meta.env.VITE_MALL_LONGITUDE) || -73.985,
    parseFloat(import.meta.env.VITE_MALL_LATITUDE) || 40.758
  ];
  const defaultZoom = parseInt(import.meta.env.VITE_MALL_ZOOM) || 18;

  useEffect(() => {
    if (map.current) return; // Initialize map only once

    // Set Mapbox access token
    mapboxgl.accessToken = MAPBOX_ACCESS_TOKEN;

    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: 'mapbox://styles/mapbox/streets-v12',
      center: defaultCenter,
      zoom: defaultZoom,
      pitch: 0,
      bearing: 0
    });

    map.current.on('load', () => {
      setMapLoaded(true);
      
      // Add navigation controls
      map.current.addControl(new mapboxgl.NavigationControl(), 'top-right');
      
      // Add scale control
      map.current.addControl(new mapboxgl.ScaleControl({
        maxWidth: 80,
        unit: 'metric'
      }), 'bottom-left');
    });

    // Cleanup function
    return () => {
      if (map.current) {
        map.current.remove();
      }
    };
  }, []);

  // Calculate and display route when both start and end points are selected
  useEffect(() => {
    if (routeStart && routeEnd && mapLoaded) {
      const route = calculateRoute(routeStart.id, routeEnd.id, selectedFloor);
      if (route && route.length > 0) {
        displayRoute(route);
      }
    } else if (mapLoaded) {
      clearRoute();
    }
  }, [routeStart, routeEnd, selectedFloor, mapLoaded, calculateRoute]);

  const displayRoute = (route) => {
    if (!map.current) return;

    // Remove existing route
    clearRoute();

    // Convert route coordinates to map coordinates
    const routeCoordinates = route.map(point => [
      defaultCenter[0] + (point.x - 500) * 0.0001, // Adjust scale as needed
      defaultCenter[1] + (point.y - 500) * 0.0001
    ]);

    // Add route source
    map.current.addSource('route', {
      type: 'geojson',
      data: {
        type: 'Feature',
        properties: {},
        geometry: {
          type: 'LineString',
          coordinates: routeCoordinates
        }
      }
    });

    // Add route layer
    map.current.addLayer({
      id: 'route',
      type: 'line',
      source: 'route',
      layout: {
        'line-join': 'round',
        'line-cap': 'round'
      },
      paint: {
        'line-color': '#3b82f6',
        'line-width': 4,
        'line-opacity': 0.8
      }
    });

    // Add route markers
    route.forEach((point, index) => {
      const isStart = index === 0;
      const isEnd = index === route.length - 1;
      
      if (isStart || isEnd) {
        const marker = new mapboxgl.Marker({
          color: isStart ? '#10b981' : '#ef4444'
        })
          .setLngLat([
            defaultCenter[0] + (point.x - 500) * 0.0001,
            defaultCenter[1] + (point.y - 500) * 0.0001
          ])
          .addTo(map.current);
      }
    });
  };

  const clearRoute = () => {
    if (!map.current) return;

    // Remove route layer and source
    if (map.current.getLayer('route')) {
      map.current.removeLayer('route');
    }
    if (map.current.getSource('route')) {
      map.current.removeSource('route');
    }

    // Remove all markers (this is a simple approach, in production you'd want to track markers)
    const markers = document.querySelectorAll('.mapboxgl-marker');
    markers.forEach(marker => marker.remove());
  };

  return (
    <div className="relative w-full h-full">
      <div ref={mapContainer} className="w-full h-full" />
      
      {mapLoaded && (
        <SVGOverlay
          map={map.current}
          selectedFloor={selectedFloor}
          onBoothClick={onBoothClick}
          onRouteSelection={onRouteSelection}
          routeStart={routeStart}
          routeEnd={routeEnd}
        />
      )}

      {/* Map Instructions */}
      <div className="absolute top-4 left-4 bg-white bg-opacity-90 backdrop-blur-sm rounded-lg p-3 shadow-lg max-w-xs hidden md:block">
        <h3 className="font-semibold text-sm text-gray-800 mb-2">Instructions:</h3>
        <ul className="text-xs text-gray-600 space-y-1">
          <li>• Click on booths to view information</li>
          <li>• Select two booths to find the shortest route</li>
          <li>• Use floor selector to switch between levels</li>
          <li>• Zoom and pan to explore the mall</li>
        </ul>
      </div>

      {/* Route Status */}
      {(routeStart || routeEnd) && (
        <div className="absolute bottom-4 left-4 right-4 md:right-auto bg-white bg-opacity-90 backdrop-blur-sm rounded-lg p-3 shadow-lg max-w-sm">
          <h3 className="font-semibold text-sm text-gray-800 mb-2">Route Planning:</h3>
          <div className="text-xs text-gray-600 space-y-1">
            {routeStart && (
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-500 rounded-full mr-2 flex-shrink-0"></div>
                <span className="truncate">Start: {routeStart.name}</span>
              </div>
            )}
            {routeEnd && (
              <div className="flex items-center">
                <div className="w-3 h-3 bg-red-500 rounded-full mr-2 flex-shrink-0"></div>
                <span className="truncate">End: {routeEnd.name}</span>
              </div>
            )}
            {routeStart && !routeEnd && (
              <div className="text-blue-600">Click another booth to set destination</div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default MapComponent;
