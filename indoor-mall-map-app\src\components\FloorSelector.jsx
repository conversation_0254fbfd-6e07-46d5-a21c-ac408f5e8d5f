import React from 'react';
import { useMallData } from '../context/MallDataContext';

const FloorSelector = ({ selectedFloor, onFloorChange }) => {
  const { mallData } = useMallData();

  return (
    <div className="flex items-center space-x-2">
      <label htmlFor="floor-select" className="text-sm font-medium text-gray-700">
        Floor:
      </label>
      <select
        id="floor-select"
        value={selectedFloor}
        onChange={(e) => onFloorChange(parseInt(e.target.value))}
        className="block w-20 px-3 py-2 text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
      >
        {mallData.floors.map((floor) => (
          <option key={floor.id} value={floor.id}>
            {floor.id}
          </option>
        ))}
      </select>
      
      {/* Floor Info */}
      <div className="hidden md:block text-xs text-gray-500">
        {mallData.floors.find(f => f.id === selectedFloor)?.booths.length || 0} booths
      </div>
    </div>
  );
};

export default FloorSelector;
