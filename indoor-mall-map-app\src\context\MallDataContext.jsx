import React, { createContext, useContext } from 'react';
import { dijkstra } from '../utils/pathfinding';

// Sample mall data
const sampleMallData = {
  floors: [
    {
      id: 1,
      name: "Ground Floor",
      booths: [
        {
          id: 1,
          name: "Coffee Corner",
          category: "Food & Beverage",
          description: "Premium coffee and pastries",
          position: { x: 200, y: 200 },
          size: { width: 80, height: 60 },
          floor: 1,
          available: true,
          amenities: ["WiFi", "Seating", "Takeaway"],
          contact: { phone: "******-0101", email: "<EMAIL>" },
          hours: "7:00 AM - 9:00 PM"
        },
        {
          id: 2,
          name: "Fashion Hub",
          category: "Clothing",
          description: "Trendy fashion for all ages",
          position: { x: 400, y: 200 },
          size: { width: 100, height: 80 },
          floor: 1,
          available: false,
          amenities: ["Fitting Rooms", "Personal Styling"],
          contact: { phone: "******-0102", email: "<EMAIL>" },
          hours: "10:00 AM - 8:00 PM"
        },
        {
          id: 3,
          name: "Tech Store",
          category: "Electronics",
          description: "Latest gadgets and accessories",
          position: { x: 600, y: 200 },
          size: { width: 90, height: 70 },
          floor: 1,
          available: true,
          amenities: ["Tech Support", "Warranty Service"],
          contact: { phone: "******-0103", email: "<EMAIL>" },
          hours: "9:00 AM - 9:00 PM"
        },
        {
          id: 4,
          name: "Book Nook",
          category: "Books & Media",
          description: "Books, magazines, and more",
          position: { x: 200, y: 400 },
          size: { width: 85, height: 65 },
          floor: 1,
          available: true,
          amenities: ["Reading Area", "Special Orders"],
          contact: { phone: "******-0104", email: "<EMAIL>" },
          hours: "8:00 AM - 10:00 PM"
        },
        {
          id: 5,
          name: "Jewelry Palace",
          category: "Jewelry",
          description: "Fine jewelry and watches",
          position: { x: 400, y: 400 },
          size: { width: 70, height: 50 },
          floor: 1,
          available: true,
          amenities: ["Custom Design", "Repair Service"],
          contact: { phone: "******-0105", email: "<EMAIL>" },
          hours: "10:00 AM - 7:00 PM"
        },
        {
          id: 6,
          name: "Sports Zone",
          category: "Sports & Recreation",
          description: "Athletic wear and equipment",
          position: { x: 600, y: 400 },
          size: { width: 95, height: 75 },
          floor: 1,
          available: false,
          amenities: ["Equipment Testing", "Size Fitting"],
          contact: { phone: "******-0106", email: "<EMAIL>" },
          hours: "9:00 AM - 8:00 PM"
        }
      ],
      hallways: [
        { id: 1, start: { x: 100, y: 200 }, end: { x: 700, y: 200 } },
        { id: 2, start: { x: 100, y: 400 }, end: { x: 700, y: 400 } },
        { id: 3, start: { x: 200, y: 100 }, end: { x: 200, y: 500 } },
        { id: 4, start: { x: 400, y: 100 }, end: { x: 400, y: 500 } },
        { id: 5, start: { x: 600, y: 100 }, end: { x: 600, y: 500 } }
      ]
    },
    {
      id: 2,
      name: "Second Floor",
      booths: [
        {
          id: 7,
          name: "Art Gallery",
          category: "Art & Culture",
          description: "Local and international art",
          position: { x: 200, y: 200 },
          size: { width: 120, height: 90 },
          floor: 2,
          available: true,
          amenities: ["Guided Tours", "Art Classes"],
          contact: { phone: "******-0107", email: "<EMAIL>" },
          hours: "11:00 AM - 6:00 PM"
        },
        {
          id: 8,
          name: "Home Decor",
          category: "Home & Garden",
          description: "Beautiful home decorations",
          position: { x: 400, y: 200 },
          size: { width: 110, height: 85 },
          floor: 2,
          available: true,
          amenities: ["Interior Design", "Delivery Service"],
          contact: { phone: "******-0108", email: "<EMAIL>" },
          hours: "10:00 AM - 7:00 PM"
        },
        {
          id: 9,
          name: "Kids Paradise",
          category: "Children",
          description: "Toys and children's items",
          position: { x: 600, y: 200 },
          size: { width: 100, height: 80 },
          floor: 2,
          available: false,
          amenities: ["Play Area", "Birthday Parties"],
          contact: { phone: "******-0109", email: "<EMAIL>" },
          hours: "9:00 AM - 8:00 PM"
        },
        {
          id: 10,
          name: "Beauty Salon",
          category: "Beauty & Wellness",
          description: "Full-service beauty salon",
          position: { x: 300, y: 400 },
          size: { width: 90, height: 70 },
          floor: 2,
          available: true,
          amenities: ["Hair Styling", "Manicure", "Facial"],
          contact: { phone: "******-0110", email: "<EMAIL>" },
          hours: "9:00 AM - 7:00 PM"
        }
      ],
      hallways: [
        { id: 6, start: { x: 100, y: 200 }, end: { x: 700, y: 200 } },
        { id: 7, start: { x: 100, y: 400 }, end: { x: 700, y: 400 } },
        { id: 8, start: { x: 200, y: 100 }, end: { x: 200, y: 500 } },
        { id: 9, start: { x: 400, y: 100 }, end: { x: 400, y: 500 } },
        { id: 10, start: { x: 600, y: 100 }, end: { x: 600, y: 500 } }
      ]
    }
  ]
};

const MallDataContext = createContext();

export const useMallData = () => {
  const context = useContext(MallDataContext);
  if (!context) {
    throw new Error('useMallData must be used within a MallDataProvider');
  }
  return context;
};

export const MallDataProvider = ({ children }) => {
  // Create a graph for pathfinding
  const createGraph = (floorId) => {
    const floor = sampleMallData.floors.find(f => f.id === floorId);
    if (!floor) return {};

    const graph = {};
    
    // Add booths as nodes
    floor.booths.forEach(booth => {
      graph[booth.id] = {};
    });

    // Add connections based on proximity and hallways
    floor.booths.forEach(booth1 => {
      floor.booths.forEach(booth2 => {
        if (booth1.id !== booth2.id) {
          const distance = Math.sqrt(
            Math.pow(booth1.position.x - booth2.position.x, 2) +
            Math.pow(booth1.position.y - booth2.position.y, 2)
          );
          
          // Connect booths that are reasonably close (within 300 units)
          if (distance <= 300) {
            graph[booth1.id][booth2.id] = distance;
          }
        }
      });
    });

    return graph;
  };

  const calculateRoute = (startId, endId, floorId) => {
    const graph = createGraph(floorId);
    const path = dijkstra(graph, startId, endId);
    
    if (!path || path.length === 0) return [];

    const floor = sampleMallData.floors.find(f => f.id === floorId);
    if (!floor) return [];

    // Convert booth IDs to coordinates
    return path.map(boothId => {
      const booth = floor.booths.find(b => b.id === boothId);
      return booth ? booth.position : null;
    }).filter(Boolean);
  };

  const value = {
    mallData: sampleMallData,
    calculateRoute
  };

  return (
    <MallDataContext.Provider value={value}>
      {children}
    </MallDataContext.Provider>
  );
};
